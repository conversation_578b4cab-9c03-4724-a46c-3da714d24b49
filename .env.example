# .env.example
# 复制此文件为 .env 并填入实际值

# 域名配置
DOMAIN=your-domain.com

# Trojan-Go 配置
TROJAN_PASSWORD=your-trojan-password-here
WEBSOCKET_PATH=/your-websocket-path-here

# 端口配置
LOCAL_PORT=443
FALLBACK_PORT=7980          # Trojan-Go 回落端口，也是 Caddy 服务端口
LOG_LEVEL=1

# SSL 证书路径
SSL_CERT=/etc/ssl/cloudflare/cert.pem
SSL_KEY=/etc/ssl/cloudflare/key.pem

# Caddy 配置
HTTPS_PORT=8443
HEALTH_CHECK_PORT=8081

# Certbot 配置
CERTBOT_EMAIL=<EMAIL>

# 用户权限配置
# 使用当前用户: USER_ID=$(id -u), GROUP_ID=$(id -g)
USER_ID=1000
GROUP_ID=1000
