# Trojan-Go Docker 项目 .gitignore

# 敏感配置文件
.env
.env.local
.env.production

# SSL 证书和密钥文件
etc/ssl/*.pem
etc/ssl/*.key
etc/ssl/*.crt
etc/ssl/*.p12
etc/ssl/*.pfx

# Certbot 生成的证书和配置
etc/certbot/conf/
etc/certbot/logs/
etc/certbot/www/.well-known/

# 生成的配置文件（从模板生成）
etc/trojan-go/config.json
etc/caddy/Caddyfile

# 日志文件
logs/
*.log
*.log.*

# Caddy 数据和缓存
etc/caddy/caddy-data/
etc/caddy/caddy-config/

# Docker 相关
.docker/
docker-compose.override.yml

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.vim/
.nvim/

# 备份文件
*.bak
*.backup
*.old

# 测试输出
test-output/
test-results/

# 运行时文件
*.pid
*.sock

# 压缩文件
*.tar.gz
*.zip
*.rar
*.7z

# 本地开发文件
local/
dev/
debug/

# 文档生成
docs/_build/
docs/build/

# 包管理器
node_modules/
vendor/

# 其他
.cache/
.tmp/
