# SSL 证书自动更新配置

本项目已集成 Certbot 服务，用于自动获取和更新 Let's Encrypt SSL 证书。

## 🚀 快速开始

### 1. 首次获取证书

在启动完整服务之前，需要先获取 SSL 证书：

```bash
# 运行初始化脚本
./scripts/init-ssl.sh
```

### 2. 启动完整服务

证书获取成功后，启动所有服务：

```bash
docker-compose up -d
```

## 📁 目录结构

```
├── etc/
│   ├── certbot/
│   │   ├── conf/          # Let's Encrypt 证书存储
│   │   ├── www/           # ACME 挑战文件
│   │   ├── logs/          # Certbot 日志
│   │   ├── renew-cert.sh  # 证书更新脚本
│   │   └── crontab        # 定时任务配置
│   └── ssl/
│       ├── cert.pem       # 当前使用的证书
│       └── key.pem        # 当前使用的私钥
├── scripts/
│   └── init-ssl.sh        # SSL 初始化脚本
└── docker-compose.yml
```

## ⚙️ 服务配置

### Certbot 服务

- **镜像**: `certbot/certbot:latest`
- **容器名**: `trojan-certbot`
- **功能**: 自动获取和更新 SSL 证书
- **定时任务**: 每天凌晨 2:30 检查证书更新

### 证书更新流程

1. Certbot 检查证书是否需要更新
2. 如需更新，通过 HTTP-01 挑战验证域名所有权
3. 获取新证书后，复制到 `./etc/ssl/` 目录
4. 自动重载 Caddy 配置
5. 自动重启 Trojan-Go 服务

## 🔧 配置说明

### 环境变量

在 `.env` 文件中配置：

```env
DOMAIN=your-domain.com
CERTBOT_EMAIL=<EMAIL>
```

### Caddy 配置

Caddy 已配置为：
- 在 80 端口处理 ACME 挑战
- 自动重定向 HTTP 到 HTTPS
- 支持证书热重载

## 📝 常用命令

### 手动更新证书

```bash
# 进入 certbot 容器手动更新
docker-compose exec certbot /usr/local/bin/renew-cert.sh
```

### 查看证书状态

```bash
# 查看证书信息
docker-compose exec certbot certbot certificates

# 查看证书有效期
openssl x509 -in ./etc/ssl/cert.pem -text -noout | grep "Not After"
```

### 查看日志

```bash
# 查看 certbot 服务日志
docker-compose logs certbot

# 查看证书更新日志
docker-compose exec certbot cat /var/log/certbot/renewal.log
```

## 🛠️ 故障排除

### 证书获取失败

1. **检查域名 DNS 解析**:
   ```bash
   nslookup your-domain.com
   ```

2. **检查 80 端口是否开放**:
   ```bash
   curl -I http://your-domain.com/.well-known/acme-challenge/test
   ```

3. **查看详细错误日志**:
   ```bash
   docker-compose logs certbot
   ```

### 证书更新失败

1. **手动触发更新**:
   ```bash
   docker-compose exec certbot certbot renew --dry-run
   ```

2. **检查 cron 服务**:
   ```bash
   docker-compose exec certbot ps aux | grep cron
   ```

## 🔒 安全注意事项

1. **私钥权限**: 私钥文件权限设置为 600
2. **容器安全**: Certbot 容器以非 root 用户运行
3. **网络隔离**: 仅开放必要的端口
4. **日志轮转**: 定期清理过期的日志文件

## 📅 维护计划

- **每日**: 自动检查证书更新（凌晨 2:30）
- **每周**: 清理过期的证书备份文件
- **每月**: 检查服务运行状态和日志
- **每季度**: 更新 Certbot 镜像版本
