#!/bin/bash

# Certbot 证书更新脚本
# 用于自动更新 Let's Encrypt 证书并重载相关服务

set -e

DOMAIN="${DOMAIN:-la.us.ucloud.vps.661224.xyz}"
EMAIL="${CERTBOT_EMAIL:-admin@${DOMAIN}}"
WEBROOT_PATH="/var/www/certbot"
CERT_PATH="/etc/letsencrypt/live/${DOMAIN}"
SSL_PATH="/etc/ssl/cloudflare"

echo "$(date): Starting certificate renewal process for domain: ${DOMAIN}"

# 检查是否是首次运行（证书不存在）
if [ ! -d "${CERT_PATH}" ]; then
    echo "$(date): First time setup - obtaining new certificate"
    
    # 首次获取证书
    certbot certonly \
        --webroot \
        --webroot-path="${WEBROOT_PATH}" \
        --email="${EMAIL}" \
        --agree-tos \
        --no-eff-email \
        --force-renewal \
        -d "${DOMAIN}"
else
    echo "$(date): Certificate exists - attempting renewal"
    
    # 尝试更新证书
    certbot renew --webroot --webroot-path="${WEBROOT_PATH}"
fi

# 检查证书是否存在并复制到指定位置
if [ -d "${CERT_PATH}" ]; then
    echo "$(date): Copying certificates to ${SSL_PATH}"
    
    # 复制证书文件
    cp "${CERT_PATH}/fullchain.pem" "${SSL_PATH}/cert.pem"
    cp "${CERT_PATH}/privkey.pem" "${SSL_PATH}/key.pem"
    
    # 设置正确的权限
    chmod 644 "${SSL_PATH}/cert.pem"
    chmod 600 "${SSL_PATH}/key.pem"
    
    echo "$(date): Certificates updated successfully"
    
    # 发送信号给其他容器重载配置
    echo "$(date): Sending reload signal to services"
    
    # 重载 Caddy 配置（如果容器存在）
    if docker ps --format "table {{.Names}}" | grep -q "trojan-caddy"; then
        echo "$(date): Reloading Caddy configuration"
        docker exec trojan-caddy caddy reload --config /etc/caddy/Caddyfile || true
    fi
    
    # 重启 Trojan-Go 服务（如果容器存在）
    if docker ps --format "table {{.Names}}" | grep -q "trojan-go-wss"; then
        echo "$(date): Restarting Trojan-Go service"
        docker restart trojan-go-wss || true
    fi
    
    echo "$(date): Certificate renewal process completed successfully"
else
    echo "$(date): ERROR - Certificate not found at ${CERT_PATH}"
    exit 1
fi
