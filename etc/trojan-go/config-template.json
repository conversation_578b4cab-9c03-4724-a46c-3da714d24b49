{
    "run_type": "server",
    "local_addr": "0.0.0.0",
    "local_port": ${LOCAL_PORT},
    "remote_addr": "127.0.0.1",
    "remote_port": ${FALLBACK_PORT},
    "log_level": ${LOG_LEVEL},
    "log_file": "/var/log/trojan/server.log",
    "password": [
        "${TROJAN_PASSWORD}"
    ],
    "ssl": {
        "verify": true,
        "verify_hostname": true,
        "cert": "${SSL_CERT}",
        "key": "${SSL_KEY}",
        "sni": "${DOMAIN}",
        "alpn": [
            "http/1.1"
        ],
        "session_ticket": true,
        "reuse_session": true
    },
    "tcp": {
        "prefer_ipv4": false,
        "no_delay": true,
        "keep_alive": true,
        "reuse_port": false,
        "fast_open": false,
        "fast_open_qlen": 20
    },
    "websocket": {
        "enabled": true,
        "path": "${WEBSOCKET_PATH}",
        "host": "la.us.ucloud.px.661224.xyz"
    },
    "mux": {
        "enabled": true,
        "concurrency": 16,
        "idle_timeout": 120
    },
    "router": {
        "enabled": true,
        "block": [
            "geoip:private"
        ],
        "geoip": "/usr/local/share/v2ray/geoip.dat",
        "geosite": "/usr/local/share/v2ray/geosite.dat"
    }
}
