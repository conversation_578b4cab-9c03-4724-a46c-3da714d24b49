#!/bin/bash

# Caddy 启动脚本
# 从环境变量生成 Caddyfile 并启动 Caddy 服务

set -e

# 配置文件路径
TEMPLATE_FILE="/etc/caddy/Caddyfile-template"
CONFIG_FILE="/etc/caddy/Caddyfile"
LOG_DIR="/var/log/caddy"

# 默认环境变量
DEFAULT_DOMAIN="localhost"
DEFAULT_FALLBACK_PORT="7980"
DEFAULT_HTTPS_PORT="8443"
DEFAULT_HEALTH_CHECK_PORT="8080"

# 从环境变量获取配置，如果未设置则使用默认值
DOMAIN="${DOMAIN:-$DEFAULT_DOMAIN}"
FALLBACK_PORT="${FALLBACK_PORT:-$DEFAULT_FALLBACK_PORT}"
HTTPS_PORT="${HTTPS_PORT:-$DEFAULT_HTTPS_PORT}"
HEALTH_CHECK_PORT="${HEALTH_CHECK_PORT:-$DEFAULT_HEALTH_CHECK_PORT}"

echo "=== Caddy 启动脚本 ==="
echo "时间: $(date)"
echo "域名: $DOMAIN"
echo "回落端口: $FALLBACK_PORT"
echo "HTTPS 端口: $HTTPS_PORT"
echo "健康检查端口: $HEALTH_CHECK_PORT"
echo ""

# 检查模板文件是否存在
if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "错误: Caddy 配置模板文件不存在: $TEMPLATE_FILE"
    exit 1
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

# 创建必要的目录
mkdir -p /var/www/html
mkdir -p /var/www/certbot

# 检查 SSL 证书文件（如果配置了 HTTPS）
if [ "$HTTPS_PORT" != "disabled" ]; then
    if [ ! -f "/etc/ssl/cert.pem" ]; then
        echo "警告: SSL 证书文件不存在: /etc/ssl/cert.pem"
        echo "将禁用 HTTPS 配置"
        HTTPS_PORT="disabled"
    fi
    
    if [ ! -f "/etc/ssl/key.pem" ]; then
        echo "警告: SSL 私钥文件不存在: /etc/ssl/key.pem"
        echo "将禁用 HTTPS 配置"
        HTTPS_PORT="disabled"
    fi
fi

# 生成配置文件
echo "生成 Caddyfile: $CONFIG_FILE"

if [ "$HTTPS_PORT" = "disabled" ]; then
    # 生成不包含 HTTPS 配置的 Caddyfile
    cat "$TEMPLATE_FILE" | \
        sed "s|\${DOMAIN}|$DOMAIN|g" | \
        sed "s|\${FALLBACK_PORT}|$FALLBACK_PORT|g" | \
        sed "s|\${HEALTH_CHECK_PORT}|$HEALTH_CHECK_PORT|g" | \
        sed '/# HTTPS 服务/,/^}$/d' > "$CONFIG_FILE"
else
    # 生成完整的 Caddyfile
    cat "$TEMPLATE_FILE" | \
        sed "s|\${DOMAIN}|$DOMAIN|g" | \
        sed "s|\${FALLBACK_PORT}|$FALLBACK_PORT|g" | \
        sed "s|\${HTTPS_PORT}|$HTTPS_PORT|g" | \
        sed "s|\${HEALTH_CHECK_PORT}|$HEALTH_CHECK_PORT|g" > "$CONFIG_FILE"
fi

# 验证配置文件
echo "验证 Caddyfile 配置..."
if caddy validate --config "$CONFIG_FILE" 2>/dev/null; then
    echo "✅ Caddyfile 配置验证通过"
else
    echo "❌ Caddyfile 配置验证失败"
    echo "配置文件内容:"
    cat "$CONFIG_FILE"
    exit 1
fi

# 创建默认的 HTML 文件
if [ ! -f "/var/www/html/index.html" ]; then
    echo "创建默认 index.html 文件..."
    cat > /var/www/html/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; }
        .status { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Our Website</h1>
        <p class="status">Server Status: Online</p>
        <p>This is a sample website served by Caddy.</p>
        <p>Server Time: $(date)</p>
        <hr>
        <small>Powered by Caddy Web Server</small>
    </div>
</body>
</html>
EOF
fi

# 创建 404 页面
if [ ! -f "/var/www/html/404.html" ]; then
    echo "创建 404 错误页面..."
    cat > /var/www/html/404.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 100px auto;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
        }
        .error-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #dc3545; font-size: 4em; margin: 0; }
        h2 { color: #6c757d; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
        <p><a href="/">Return to Home</a></p>
    </div>
</body>
</html>
EOF
fi

# 显示生成的配置文件内容
echo ""
echo "生成的 Caddyfile 内容:"
echo "========================"
cat "$CONFIG_FILE"
echo "========================"
echo ""

echo "启动 Caddy 服务..."
echo "配置文件: $CONFIG_FILE"
echo "日志目录: $LOG_DIR"
echo ""

# 设置日志文件权限（如果环境变量存在）
if [ -n "$USER_ID" ] && [ -n "$GROUP_ID" ]; then
    echo "设置日志文件权限为 $USER_ID:$GROUP_ID"

    # 创建并设置日志文件权限
    touch "$LOG_DIR/access.log" "$LOG_DIR/https_access.log"
    chown "$USER_ID:$GROUP_ID" "$LOG_DIR/access.log" "$LOG_DIR/https_access.log"
    chmod 664 "$LOG_DIR/access.log" "$LOG_DIR/https_access.log"

    # 设置日志目录权限
    chown "$USER_ID:$GROUP_ID" "$LOG_DIR"
    chmod 775 "$LOG_DIR"
fi

# 启动 Caddy
exec caddy run --config "$CONFIG_FILE"
