# Caddy 配置模板
# 支持环境变量动态配置

# HTTP 服务 - 用于 ACME 挑战和重定向
:80 {
    # 处理 Let's Encrypt ACME 挑战 - 优先级最高
    handle /.well-known/acme-challenge/* {
        root * /var/www/certbot
        file_server
    }

    # 处理根域名的 ACME 挑战
    handle {
        @acme_challenge {
            path /.well-known/acme-challenge/*
        }
        root @acme_challenge /var/www/certbot
        file_server @acme_challenge

        # 其他请求重定向到 HTTPS
        redir https://{host}{uri} permanent
    }
}

# 伪装 Web 服务 - 为 Trojan-Go 提供回落服务
:${FALLBACK_PORT} {
    # 设置根目录
    root * /var/www/html
    
    # 启用文件服务器
    file_server
    
    # 添加一些基本的响应头
    header {
        Server "nginx/1.18.0"
        X-Frame-Options "SAMEORIGIN"
        X-Content-Type-Options "nosniff"
        X-XSS-Protection "1; mode=block"
    }
    
    # 处理 404 错误，返回一个简单的页面
    handle_errors {
        @404 {
            expression {http.error.status_code} == 404
        }
        rewrite @404 /404.html
        file_server
    }
    
    # 日志记录
    log {
        output file /var/log/caddy/access.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
    }
}

# HTTPS 服务 - 主要的 Web 服务（如果需要）
${DOMAIN}:${HTTPS_PORT} {
    # TLS 配置
    tls /etc/ssl/cert.pem /etc/ssl/key.pem
    
    # 设置根目录
    root * /var/www/html
    
    # 启用文件服务器
    file_server
    
    # 添加安全响应头
    header {
        Server "nginx/1.18.0"
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-Frame-Options "SAMEORIGIN"
        X-Content-Type-Options "nosniff"
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
    }
    
    # 处理静态资源缓存
    @static {
        file
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static Cache-Control "public, max-age=31536000"
    
    # 处理 404 错误
    handle_errors {
        @404 {
            expression {http.error.status_code} == 404
        }
        rewrite @404 /404.html
        file_server
    }
    
    # 日志记录
    log {
        output file /var/log/caddy/https_access.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
    }
}

# 健康检查端点
:${HEALTH_CHECK_PORT} {
    respond /health "OK" 200
    respond /status "Caddy is running" 200
    
    # 简单的状态页面
    handle / {
        respond "Caddy Server Status: Running" 200
    }
}
