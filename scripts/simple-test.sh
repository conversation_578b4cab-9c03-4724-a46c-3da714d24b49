#!/bin/bash

# 简单的 Trojan-Go 启动脚本测试

set -e

echo "=== 简单测试：Trojan-Go 启动脚本 ==="
echo ""

# 检查必要文件
echo "1. 检查文件存在性..."
files=(
    "etc/trojan-go/start-trojan.sh"
    "etc/trojan-go/config-template.json"
    ".env"
    "docker-compose.yml"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        exit 1
    fi
done

# 检查脚本权限
echo ""
echo "2. 检查脚本权限..."
if [ -x "etc/trojan-go/start-trojan.sh" ]; then
    echo "  ✅ start-trojan.sh 可执行"
else
    echo "  ❌ start-trojan.sh 不可执行"
    chmod +x etc/trojan-go/start-trojan.sh
    echo "  🔧 已修复权限"
fi

# 验证环境变量
echo ""
echo "3. 验证环境变量..."
source .env

required_vars=(
    "DOMAIN"
    "TROJAN_PASSWORD"
    "WEBSOCKET_PATH"
    "LOCAL_PORT"
    "REMOTE_PORT"
)

for var in "${required_vars[@]}"; do
    if [ -n "${!var}" ]; then
        if [ "$var" = "TROJAN_PASSWORD" ]; then
            echo "  ✅ $var: ***HIDDEN***"
        else
            echo "  ✅ $var: ${!var}"
        fi
    else
        echo "  ❌ $var: 未设置"
        exit 1
    fi
done

# 验证 docker-compose 配置
echo ""
echo "4. 验证 docker-compose 配置..."
if docker-compose config > /dev/null 2>&1; then
    echo "  ✅ docker-compose.yml 语法正确"
else
    echo "  ❌ docker-compose.yml 语法错误"
    exit 1
fi

# 测试配置模板替换
echo ""
echo "5. 测试配置模板..."
TEMP_CONFIG="/tmp/test-config-$$.json"

# 使用环境变量替换模板
cat etc/trojan-go/config-template.json | \
    sed "s|\${DOMAIN}|$DOMAIN|g" | \
    sed "s|\${TROJAN_PASSWORD}|$TROJAN_PASSWORD|g" | \
    sed "s|\${WEBSOCKET_PATH}|$WEBSOCKET_PATH|g" | \
    sed "s|\${LOCAL_PORT}|$LOCAL_PORT|g" | \
    sed "s|\${REMOTE_PORT}|$REMOTE_PORT|g" | \
    sed "s|\${LOG_LEVEL}|${LOG_LEVEL:-1}|g" | \
    sed "s|\${SSL_CERT}|${SSL_CERT:-/etc/ssl/cloudflare/cert.pem}|g" | \
    sed "s|\${SSL_KEY}|${SSL_KEY:-/etc/ssl/cloudflare/key.pem}|g" > "$TEMP_CONFIG"

# 验证生成的 JSON
if command -v jq >/dev/null 2>&1; then
    if jq empty "$TEMP_CONFIG" 2>/dev/null; then
        echo "  ✅ 配置模板替换成功，JSON 格式正确"
    else
        echo "  ❌ 生成的配置 JSON 格式错误"
        cat "$TEMP_CONFIG"
        rm -f "$TEMP_CONFIG"
        exit 1
    fi
    
    echo ""
    echo "生成的配置预览（隐藏密码）:"
    jq '.password = ["***HIDDEN***"]' "$TEMP_CONFIG"
else
    echo "  ⚠️  jq 未安装，跳过 JSON 验证"
    echo ""
    echo "生成的配置预览（隐藏密码）:"
    cat "$TEMP_CONFIG" | sed "s/\"$TROJAN_PASSWORD\"/\"***HIDDEN***/g"
fi

# 清理临时文件
rm -f "$TEMP_CONFIG"

echo ""
echo "🎉 所有测试通过！"
echo ""
echo "下一步可以执行:"
echo "  1. 测试单个服务: docker-compose up trojan-go-wss"
echo "  2. 启动所有服务: docker-compose up -d"
