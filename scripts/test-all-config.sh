#!/bin/bash

# 综合配置测试脚本
# 验证所有服务的配置和集成

set -e

echo "=== 综合配置测试脚本 ==="
echo "测试 Trojan-Go + Caddy + Certbot 完整配置"
echo ""

# 加载环境变量
if [ -f ".env" ]; then
    source .env
    echo "✅ 环境变量加载成功"
else
    echo "❌ .env 文件不存在"
    exit 1
fi

echo ""
echo "1. 环境变量检查..."
echo "   域名: $DOMAIN"
echo "   Trojan 密码: ***HIDDEN***"
echo "   WebSocket 路径: $WEBSOCKET_PATH"
echo "   Trojan 本地端口: $LOCAL_PORT"
echo "   回落端口: $FALLBACK_PORT"
echo "   HTTPS 端口: $HTTPS_PORT"
echo "   健康检查端口: $HEALTH_CHECK_PORT"

# 检查回落端口配置
echo ""
echo "2. 端口配置验证..."
echo "   ✅ 使用统一的回落端口配置: $FALLBACK_PORT"
echo "   ✅ Trojan-Go 和 Caddy 共享同一端口，避免配置冗余"

# 检查必要文件
echo ""
echo "3. 配置文件检查..."
files=(
    "docker-compose.yml"
    ".env"
    "etc/trojan-go/start-trojan.sh"
    "etc/trojan-go/config-template.json"
    "etc/caddy/start-caddy.sh"
    "etc/caddy/Caddyfile-template"
    "etc/certbot/renew-cert.sh"
    "etc/certbot/crontab"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (缺失)"
        exit 1
    fi
done

# 检查脚本权限
echo ""
echo "4. 脚本权限检查..."
scripts=(
    "etc/trojan-go/start-trojan.sh"
    "etc/caddy/start-caddy.sh"
    "etc/certbot/renew-cert.sh"
    "scripts/simple-test.sh"
    "scripts/test-caddy-config.sh"
    "scripts/init-ssl.sh"
)

for script in "${scripts[@]}"; do
    if [ -x "$script" ]; then
        echo "   ✅ $script (可执行)"
    else
        echo "   ❌ $script (不可执行)"
        chmod +x "$script"
        echo "   🔧 已修复权限: $script"
    fi
done

# 检查目录结构
echo ""
echo "5. 目录结构检查..."
dirs=(
    "etc/trojan-go"
    "etc/caddy"
    "etc/certbot/conf"
    "etc/certbot/www"
    "etc/certbot/logs"
    "etc/ssl"
    "logs/trojan-go"
    "logs/caddy"
    "scripts"
)

for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "   ✅ $dir/"
    else
        echo "   ❌ $dir/ (缺失)"
        mkdir -p "$dir"
        echo "   🔧 已创建目录: $dir/"
    fi
done

# 验证 docker-compose 配置
echo ""
echo "6. Docker Compose 配置验证..."
if docker-compose config > /dev/null 2>&1; then
    echo "   ✅ docker-compose.yml 语法正确"
else
    echo "   ❌ docker-compose.yml 语法错误"
    docker-compose config
    exit 1
fi

# 测试 Trojan-Go 配置生成
echo ""
echo "7. Trojan-Go 配置测试..."
TEMP_TROJAN_CONFIG="/tmp/test-trojan-config-$$.json"
cat etc/trojan-go/config-template.json | \
    sed "s|\${DOMAIN}|$DOMAIN|g" | \
    sed "s|\${TROJAN_PASSWORD}|$TROJAN_PASSWORD|g" | \
    sed "s|\${WEBSOCKET_PATH}|$WEBSOCKET_PATH|g" | \
    sed "s|\${LOCAL_PORT}|$LOCAL_PORT|g" | \
    sed "s|\${FALLBACK_PORT}|$FALLBACK_PORT|g" | \
    sed "s|\${LOG_LEVEL}|${LOG_LEVEL:-1}|g" | \
    sed "s|\${SSL_CERT}|${SSL_CERT:-/etc/ssl/cloudflare/cert.pem}|g" | \
    sed "s|\${SSL_KEY}|${SSL_KEY:-/etc/ssl/cloudflare/key.pem}|g" > "$TEMP_TROJAN_CONFIG"

if command -v jq >/dev/null 2>&1; then
    if jq empty "$TEMP_TROJAN_CONFIG" 2>/dev/null; then
        echo "   ✅ Trojan-Go 配置生成成功"
    else
        echo "   ❌ Trojan-Go 配置 JSON 格式错误"
        rm -f "$TEMP_TROJAN_CONFIG"
        exit 1
    fi
else
    echo "   ⚠️  jq 未安装，跳过 JSON 验证"
fi
rm -f "$TEMP_TROJAN_CONFIG"

# 测试 Caddy 配置生成
echo ""
echo "8. Caddy 配置测试..."
TEMP_CADDY_CONFIG="/tmp/test-caddy-config-$$.conf"
cat etc/caddy/Caddyfile-template | \
    sed "s|\${DOMAIN}|$DOMAIN|g" | \
    sed "s|\${FALLBACK_PORT}|$FALLBACK_PORT|g" | \
    sed "s|\${HTTPS_PORT}|disabled|g" | \
    sed "s|\${HEALTH_CHECK_PORT}|$HEALTH_CHECK_PORT|g" | \
    sed '/# HTTPS 服务/,/^}$/d' > "$TEMP_CADDY_CONFIG"

if grep -q ":$FALLBACK_PORT" "$TEMP_CADDY_CONFIG" && grep -q ":$HEALTH_CHECK_PORT" "$TEMP_CADDY_CONFIG"; then
    echo "   ✅ Caddy 配置生成成功"
else
    echo "   ❌ Caddy 配置生成失败"
    rm -f "$TEMP_CADDY_CONFIG"
    exit 1
fi
rm -f "$TEMP_CADDY_CONFIG"

# 检查 SSL 证书文件
echo ""
echo "9. SSL 证书检查..."
if [ -f "etc/ssl/cert.pem" ] && [ -f "etc/ssl/key.pem" ]; then
    echo "   ✅ SSL 证书文件存在"
    
    # 验证证书有效性
    if openssl x509 -in etc/ssl/cert.pem -noout -checkend 86400 2>/dev/null; then
        echo "   ✅ SSL 证书有效（24小时内不会过期）"
    else
        echo "   ⚠️  SSL 证书可能已过期或即将过期"
    fi
else
    echo "   ⚠️  SSL 证书文件不存在，需要运行 ./scripts/init-ssl.sh 获取证书"
fi

# 检查服务依赖
echo ""
echo "10. 服务依赖检查..."
if command -v docker >/dev/null 2>&1; then
    echo "    ✅ Docker 已安装"
else
    echo "    ❌ Docker 未安装"
    exit 1
fi

if command -v docker-compose >/dev/null 2>&1; then
    echo "    ✅ Docker Compose 已安装"
else
    echo "    ❌ Docker Compose 未安装"
    exit 1
fi

if docker ps >/dev/null 2>&1; then
    echo "    ✅ Docker 服务运行正常"
else
    echo "    ❌ Docker 服务不可用"
    exit 1
fi

echo ""
echo "🎉 所有配置检查通过！"
echo ""
echo "=== 服务启动指南 ==="
echo ""
echo "1. 首次使用（获取 SSL 证书）:"
echo "   ./scripts/init-ssl.sh"
echo ""
echo "2. 启动所有服务:"
echo "   docker-compose up -d"
echo ""
echo "3. 查看服务状态:"
echo "   docker-compose ps"
echo ""
echo "4. 查看日志:"
echo "   docker-compose logs -f"
echo ""
echo "=== 服务端点 ==="
echo "- HTTP (ACME): http://$DOMAIN (端口 80)"
echo "- 伪装 Web: http://$DOMAIN:$FALLBACK_PORT"
echo "- 健康检查: http://$DOMAIN:$HEALTH_CHECK_PORT/health"
echo "- Trojan-Go: $DOMAIN:$LOCAL_PORT"
echo ""
echo "=== 重要提醒 ==="
echo "- 确保域名 DNS 正确指向此服务器"
echo "- 确保防火墙开放必要端口: 80, 443, $FALLBACK_PORT, $HEALTH_CHECK_PORT"
echo "- 定期检查证书更新日志: docker-compose logs trojan-certbot"
