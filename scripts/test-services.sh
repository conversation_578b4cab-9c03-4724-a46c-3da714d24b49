#!/bin/bash

# 服务状态检查脚本
# 验证所有 Trojan 服务是否正常运行

set -e

echo "=== Trojan 服务状态检查 ==="
echo "时间: $(date)"
echo

# 检查 Docker 容器状态
echo "1. 检查容器状态:"
docker-compose ps
echo

# 检查端口监听状态
echo "2. 检查端口监听状态:"
echo "端口 80 (HTTP):"
ss -tlnp | grep :80 || echo "  端口 80 未监听"

echo "端口 443 (Trojan-Go):"
ss -tlnp | grep :443 || echo "  端口 443 未监听"

echo "端口 7980 (Caddy 回落):"
ss -tlnp | grep :7980 || echo "  端口 7980 未监听"

echo "端口 8081 (健康检查):"
ss -tlnp | grep :8081 || echo "  端口 8081 未监听"
echo

# 检查健康检查端点
echo "3. 检查健康检查端点:"
if curl -s http://localhost:8081/health | grep -q "OK"; then
    echo "  ✓ 健康检查端点正常"
else
    echo "  ✗ 健康检查端点异常"
fi
echo

# 检查 HTTP 重定向
echo "4. 检查 HTTP 重定向:"
if curl -s -I http://la.us.ucloud.vps.661224.xyz | grep -q "301"; then
    echo "  ✓ HTTP 重定向正常"
else
    echo "  ✗ HTTP 重定向异常"
fi
echo

# 检查 HTTPS 回落
echo "5. 检查 HTTPS 回落:"
if curl -s -I https://la.us.ucloud.vps.661224.xyz | grep -q "200"; then
    echo "  ✓ HTTPS 回落正常"
else
    echo "  ✗ HTTPS 回落异常"
fi
echo

# 检查 ACME 挑战路径
echo "6. 检查 ACME 挑战路径:"
if curl -s http://la.us.ucloud.vps.661224.xyz/.well-known/acme-challenge/test | grep -q "test"; then
    echo "  ✓ ACME 挑战路径正常"
else
    echo "  ✗ ACME 挑战路径异常"
fi
echo

# 检查证书信息
echo "7. 检查证书信息:"
if [ -f "etc/ssl/cert.pem" ]; then
    echo "  证书文件存在"
    echo "  证书主题: $(openssl x509 -in etc/ssl/cert.pem -noout -subject | cut -d= -f2-)"
    echo "  证书颁发者: $(openssl x509 -in etc/ssl/cert.pem -noout -issuer | cut -d= -f2-)"
    echo "  证书到期时间: $(openssl x509 -in etc/ssl/cert.pem -noout -enddate | cut -d= -f2)"
else
    echo "  ✗ 证书文件不存在"
fi
echo

# 检查最近的 Trojan-Go 日志
echo "8. 最近的 Trojan-Go 日志 (最后 5 行):"
if [ -f "logs/trojan-go/server.log" ]; then
    tail -5 logs/trojan-go/server.log
else
    echo "  日志文件不存在"
fi
echo

echo "=== 检查完成 ==="
