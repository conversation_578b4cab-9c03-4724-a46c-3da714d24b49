#!/bin/bash

# 配置测试脚本
# 验证 certbot 配置是否正确

set -e

echo "=== Certbot 配置测试 ==="
echo ""

# 检查必要文件是否存在
echo "1. 检查配置文件..."
files=(
    "docker-compose.yml"
    ".env"
    "etc/certbot/renew-cert.sh"
    "etc/certbot/crontab"
    "etc/caddy/Caddyfile"
    "scripts/init-ssl.sh"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        exit 1
    fi
done

# 检查目录结构
echo ""
echo "2. 检查目录结构..."
dirs=(
    "etc/certbot/conf"
    "etc/certbot/www"
    "etc/certbot/logs"
    "etc/ssl"
    "scripts"
)

for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  ✅ $dir/"
    else
        echo "  ❌ $dir/ (缺失)"
        exit 1
    fi
done

# 检查脚本权限
echo ""
echo "3. 检查脚本权限..."
scripts=(
    "etc/certbot/renew-cert.sh"
    "scripts/init-ssl.sh"
    "scripts/test-config.sh"
)

for script in "${scripts[@]}"; do
    if [ -x "$script" ]; then
        echo "  ✅ $script (可执行)"
    else
        echo "  ❌ $script (不可执行)"
        chmod +x "$script"
        echo "  🔧 已修复权限: $script"
    fi
done

# 检查环境变量
echo ""
echo "4. 检查环境变量..."
if [ -f ".env" ]; then
    source .env
    if [ -n "$DOMAIN" ]; then
        echo "  ✅ DOMAIN: $DOMAIN"
    else
        echo "  ❌ DOMAIN 未设置"
        exit 1
    fi
    
    if [ -n "$CERTBOT_EMAIL" ]; then
        echo "  ✅ CERTBOT_EMAIL: $CERTBOT_EMAIL"
    else
        echo "  ❌ CERTBOT_EMAIL 未设置"
        exit 1
    fi
fi

# 验证 docker-compose 配置
echo ""
echo "5. 验证 docker-compose 配置..."
if docker-compose config > /dev/null 2>&1; then
    echo "  ✅ docker-compose.yml 语法正确"
else
    echo "  ❌ docker-compose.yml 语法错误"
    docker-compose config
    exit 1
fi

# 检查 Docker 服务状态
echo ""
echo "6. 检查 Docker 服务..."
if docker ps > /dev/null 2>&1; then
    echo "  ✅ Docker 服务运行正常"
else
    echo "  ❌ Docker 服务不可用"
    exit 1
fi

echo ""
echo "🎉 所有配置检查通过！"
echo ""
echo "下一步操作："
echo "  1. 确保域名 DNS 已正确配置"
echo "  2. 确保防火墙开放 80 和 443 端口"
echo "  3. 运行初始化脚本: ./scripts/init-ssl.sh"
echo "  4. 启动完整服务: docker-compose up -d"
