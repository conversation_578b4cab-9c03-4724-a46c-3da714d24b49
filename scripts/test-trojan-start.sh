#!/bin/bash

# Trojan-Go 启动脚本测试
# 验证启动脚本能正确生成配置文件

set -e

echo "=== Trojan-Go 启动脚本测试 ==="
echo ""

# 设置测试环境变量
export DOMAIN="test.example.com"
export TROJAN_PASSWORD="test-password-123"
export WEBSOCKET_PATH="/test-websocket"
export LOCAL_PORT="8443"
export REMOTE_PORT="8080"
export LOG_LEVEL="2"
export SSL_CERT="/test/cert.pem"
export SSL_KEY="/test/key.pem"

echo "1. 测试环境变量设置:"
echo "   DOMAIN: $DOMAIN"
echo "   TROJAN_PASSWORD: ***HIDDEN***"
echo "   WEBSOCKET_PATH: $WEBSOCKET_PATH"
echo "   LOCAL_PORT: $LOCAL_PORT"
echo "   REMOTE_PORT: $REMOTE_PORT"
echo "   LOG_LEVEL: $LOG_LEVEL"
echo "   SSL_CERT: $SSL_CERT"
echo "   SSL_KEY: $SSL_KEY"
echo ""

# 检查启动脚本是否存在
SCRIPT_PATH="etc/trojan-go/start-trojan.sh"
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "❌ 启动脚本不存在: $SCRIPT_PATH"
    exit 1
fi

if [ ! -x "$SCRIPT_PATH" ]; then
    echo "❌ 启动脚本不可执行: $SCRIPT_PATH"
    exit 1
fi

echo "✅ 启动脚本存在且可执行"
echo ""

# 检查配置模板是否存在
TEMPLATE_PATH="etc/trojan-go/config-template.json"
if [ ! -f "$TEMPLATE_PATH" ]; then
    echo "❌ 配置模板不存在: $TEMPLATE_PATH"
    exit 1
fi

echo "✅ 配置模板存在"
echo ""

# 创建临时目录进行测试
TEST_DIR="/tmp/trojan-test-$$"
mkdir -p "$TEST_DIR/etc/trojan-go"
mkdir -p "$TEST_DIR/var/log/trojan"

# 复制文件到测试目录
cp "$TEMPLATE_PATH" "$TEST_DIR/etc/trojan-go/"
cp "$SCRIPT_PATH" "$TEST_DIR/etc/trojan-go/"

echo "2. 测试配置文件生成..."

# 修改脚本中的路径为测试路径
sed -i "s|/etc/trojan-go|$TEST_DIR/etc/trojan-go|g" "$TEST_DIR/etc/trojan-go/start-trojan.sh"
sed -i "s|/var/log/trojan|$TEST_DIR/var/log/trojan|g" "$TEST_DIR/etc/trojan-go/start-trojan.sh"

# 修改脚本，只生成配置文件不启动服务
cat > "$TEST_DIR/test-config-gen.sh" << EOF
#!/bin/bash
set -e

# 从启动脚本中提取配置生成部分
TEMPLATE_FILE="$TEST_DIR/etc/trojan-go/config-template.json"
CONFIG_FILE="$TEST_DIR/etc/trojan-go/config.json"
LOG_FILE="$TEST_DIR/var/log/trojan/server.log"

# 默认环境变量
DEFAULT_DOMAIN="localhost"
DEFAULT_PASSWORD="your-password-here"
DEFAULT_WEBSOCKET_PATH="/websocket"
DEFAULT_LOCAL_PORT="443"
DEFAULT_REMOTE_PORT="80"
DEFAULT_LOG_LEVEL="1"
DEFAULT_SSL_CERT="/etc/ssl/cloudflare/cert.pem"
DEFAULT_SSL_KEY="/etc/ssl/cloudflare/key.pem"

# 从环境变量获取配置
DOMAIN="${DOMAIN:-$DEFAULT_DOMAIN}"
TROJAN_PASSWORD="${TROJAN_PASSWORD:-$DEFAULT_PASSWORD}"
WEBSOCKET_PATH="${WEBSOCKET_PATH:-$DEFAULT_WEBSOCKET_PATH}"
LOCAL_PORT="${LOCAL_PORT:-$DEFAULT_LOCAL_PORT}"
REMOTE_PORT="${REMOTE_PORT:-$DEFAULT_REMOTE_PORT}"
LOG_LEVEL="${LOG_LEVEL:-$DEFAULT_LOG_LEVEL}"
SSL_CERT="${SSL_CERT:-$DEFAULT_SSL_CERT}"
SSL_KEY="${SSL_KEY:-$DEFAULT_SSL_KEY}"

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# 生成配置文件
cat "$TEMPLATE_FILE" | \
    sed "s|\${DOMAIN}|$DOMAIN|g" | \
    sed "s|\${TROJAN_PASSWORD}|$TROJAN_PASSWORD|g" | \
    sed "s|\${WEBSOCKET_PATH}|$WEBSOCKET_PATH|g" | \
    sed "s|\${LOCAL_PORT}|$LOCAL_PORT|g" | \
    sed "s|\${REMOTE_PORT}|$REMOTE_PORT|g" | \
    sed "s|\${LOG_LEVEL}|$LOG_LEVEL|g" | \
    sed "s|\${SSL_CERT}|$SSL_CERT|g" | \
    sed "s|\${SSL_KEY}|$SSL_KEY|g" > "$CONFIG_FILE"

echo "配置文件已生成: $CONFIG_FILE"
EOF

chmod +x "$TEST_DIR/test-config-gen.sh"

# 运行配置生成测试
if "$TEST_DIR/test-config-gen.sh"; then
    echo "✅ 配置文件生成成功"
else
    echo "❌ 配置文件生成失败"
    exit 1
fi

# 验证生成的配置文件
CONFIG_FILE="$TEST_DIR/etc/trojan-go/config.json"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 配置文件存在: $CONFIG_FILE"
    
    # 检查 JSON 格式
    if command -v jq >/dev/null 2>&1; then
        if jq empty "$CONFIG_FILE" 2>/dev/null; then
            echo "✅ 配置文件 JSON 格式正确"
        else
            echo "❌ 配置文件 JSON 格式错误"
            exit 1
        fi
    else
        echo "⚠️  jq 未安装，跳过 JSON 格式验证"
    fi
    
    echo ""
    echo "3. 生成的配置文件内容:"
    if command -v jq >/dev/null 2>&1; then
        jq '.password = ["***HIDDEN***"]' "$CONFIG_FILE"
    else
        cat "$CONFIG_FILE" | sed 's/"test-password-123"/"***HIDDEN***"/g'
    fi
else
    echo "❌ 配置文件未生成"
    exit 1
fi

# 验证配置内容
echo ""
echo "4. 验证配置内容..."

# 检查关键配置项
if grep -q "\"$DOMAIN\"" "$CONFIG_FILE"; then
    echo "✅ 域名配置正确"
else
    echo "❌ 域名配置错误"
    exit 1
fi

if grep -q "\"$WEBSOCKET_PATH\"" "$CONFIG_FILE"; then
    echo "✅ WebSocket 路径配置正确"
else
    echo "❌ WebSocket 路径配置错误"
    exit 1
fi

if grep -q "\"local_port\": $LOCAL_PORT" "$CONFIG_FILE"; then
    echo "✅ 本地端口配置正确"
else
    echo "❌ 本地端口配置错误"
    exit 1
fi

if grep -q "\"remote_port\": $REMOTE_PORT" "$CONFIG_FILE"; then
    echo "✅ 远程端口配置正确"
else
    echo "❌ 远程端口配置错误"
    exit 1
fi

# 清理测试目录
rm -rf "$TEST_DIR"

echo ""
echo "🎉 所有测试通过！启动脚本工作正常。"
echo ""
echo "现在可以使用以下命令测试完整的服务:"
echo "  docker-compose up trojan-go-wss"
