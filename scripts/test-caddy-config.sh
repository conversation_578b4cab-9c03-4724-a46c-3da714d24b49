#!/bin/bash

# Caddy 配置测试脚本
# 验证 Caddy 启动脚本和配置生成功能

set -e

echo "=== Caddy 配置测试脚本 ==="
echo ""

# 设置测试环境变量
export DOMAIN="test.example.com"
export FALLBACK_PORT="8980"
export HTTPS_PORT="8443"
export HEALTH_CHECK_PORT="8081"

echo "1. 测试环境变量设置:"
echo "   DOMAIN: $DOMAIN"
echo "   FALLBACK_PORT: $FALLBACK_PORT"
echo "   HTTPS_PORT: $HTTPS_PORT"
echo "   HEALTH_CHECK_PORT: $HEALTH_CHECK_PORT"
echo ""

# 检查必要文件
echo "2. 检查必要文件..."
files=(
    "etc/caddy/start-caddy.sh"
    "etc/caddy/Caddyfile-template"
    ".env"
    "docker-compose.yml"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        exit 1
    fi
done

# 检查脚本权限
echo ""
echo "3. 检查脚本权限..."
if [ -x "etc/caddy/start-caddy.sh" ]; then
    echo "  ✅ start-caddy.sh 可执行"
else
    echo "  ❌ start-caddy.sh 不可执行"
    chmod +x etc/caddy/start-caddy.sh
    echo "  🔧 已修复权限"
fi

# 创建临时测试目录
TEST_DIR="/tmp/caddy-test-$$"
mkdir -p "$TEST_DIR/etc/caddy"
mkdir -p "$TEST_DIR/var/www/html"
mkdir -p "$TEST_DIR/var/log/caddy"

echo ""
echo "4. 测试配置文件生成..."

# 复制文件到测试目录
cp "etc/caddy/Caddyfile-template" "$TEST_DIR/etc/caddy/"
cp "etc/caddy/start-caddy.sh" "$TEST_DIR/etc/caddy/"

# 修改脚本中的路径为测试路径
sed -i "s|/etc/caddy|$TEST_DIR/etc/caddy|g" "$TEST_DIR/etc/caddy/start-caddy.sh"
sed -i "s|/var/www|$TEST_DIR/var/www|g" "$TEST_DIR/etc/caddy/start-caddy.sh"
sed -i "s|/var/log/caddy|$TEST_DIR/var/log/caddy|g" "$TEST_DIR/etc/caddy/start-caddy.sh"

# 创建配置生成测试脚本
cat > "$TEST_DIR/test-config-gen.sh" << EOF
#!/bin/bash
set -e

TEMPLATE_FILE="$TEST_DIR/etc/caddy/Caddyfile-template"
CONFIG_FILE="$TEST_DIR/etc/caddy/Caddyfile"

# 从环境变量获取配置
DOMAIN="\${DOMAIN:-localhost}"
FALLBACK_PORT="\${FALLBACK_PORT:-7980}"
HTTPS_PORT="\${HTTPS_PORT:-8443}"
HEALTH_CHECK_PORT="\${HEALTH_CHECK_PORT:-8080}"

# 创建必要目录
mkdir -p "$TEST_DIR/var/www/html"
mkdir -p "$TEST_DIR/var/log/caddy"

# 生成配置文件（禁用 HTTPS 以避免证书检查）
HTTPS_PORT="disabled"

if [ "\$HTTPS_PORT" = "disabled" ]; then
    cat "\$TEMPLATE_FILE" | \\
        sed "s|\\\${DOMAIN}|\$DOMAIN|g" | \\
        sed "s|\\\${FALLBACK_PORT}|\$FALLBACK_PORT|g" | \\
        sed "s|\\\${HEALTH_CHECK_PORT}|\$HEALTH_CHECK_PORT|g" | \\
        sed '/# HTTPS 服务/,/^}\$/d' > "\$CONFIG_FILE"
else
    cat "\$TEMPLATE_FILE" | \\
        sed "s|\\\${DOMAIN}|\$DOMAIN|g" | \\
        sed "s|\\\${FALLBACK_PORT}|\$FALLBACK_PORT|g" | \\
        sed "s|\\\${HTTPS_PORT}|\$HTTPS_PORT|g" | \\
        sed "s|\\\${HEALTH_CHECK_PORT}|\$HEALTH_CHECK_PORT|g" > "\$CONFIG_FILE"
fi

echo "配置文件已生成: \$CONFIG_FILE"
EOF

chmod +x "$TEST_DIR/test-config-gen.sh"

# 运行配置生成测试
if "$TEST_DIR/test-config-gen.sh"; then
    echo "  ✅ 配置文件生成成功"
else
    echo "  ❌ 配置文件生成失败"
    exit 1
fi

# 验证生成的配置文件
CONFIG_FILE="$TEST_DIR/etc/caddy/Caddyfile"
if [ -f "$CONFIG_FILE" ]; then
    echo "  ✅ 配置文件存在: $CONFIG_FILE"
    
    echo ""
    echo "5. 生成的 Caddyfile 内容:"
    echo "=========================="
    cat "$CONFIG_FILE"
    echo "=========================="
else
    echo "  ❌ 配置文件未生成"
    exit 1
fi

# 验证配置内容
echo ""
echo "6. 验证配置内容..."

# 检查关键配置项
if grep -q ":$FALLBACK_PORT" "$CONFIG_FILE"; then
    echo "  ✅ 回落端口配置正确"
else
    echo "  ❌ 回落端口配置错误"
    exit 1
fi

if grep -q ":$HEALTH_CHECK_PORT" "$CONFIG_FILE"; then
    echo "  ✅ 健康检查端口配置正确"
else
    echo "  ❌ 健康检查端口配置错误"
    exit 1
fi

if grep -q "/.well-known/acme-challenge/" "$CONFIG_FILE"; then
    echo "  ✅ ACME 挑战配置正确"
else
    echo "  ❌ ACME 挑战配置错误"
    exit 1
fi

# 检查是否正确禁用了 HTTPS 配置
if ! grep -q "$DOMAIN:$HTTPS_PORT" "$CONFIG_FILE"; then
    echo "  ✅ HTTPS 配置已正确禁用"
else
    echo "  ❌ HTTPS 配置未正确禁用"
    exit 1
fi

# 清理测试目录
rm -rf "$TEST_DIR"

echo ""
echo "🎉 所有测试通过！Caddy 配置脚本工作正常。"
echo ""
echo "现在可以使用以下命令测试完整的服务:"
echo "  1. 测试 Caddy 服务: docker-compose up trojan-caddy"
echo "  2. 测试所有服务: docker-compose up -d"
echo ""
echo "重要提醒:"
echo "  - Trojan-Go 和 Caddy 共享同一个回落端口 (FALLBACK_PORT)"
echo "  - 当前配置: FALLBACK_PORT=$FALLBACK_PORT"

# 检查端口配置
source .env
echo "  ✅ 使用统一的回落端口配置: $FALLBACK_PORT"
