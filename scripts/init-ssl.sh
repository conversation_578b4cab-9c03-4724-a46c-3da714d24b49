#!/bin/bash

# SSL 证书初始化脚本
# 用于首次获取 Let's Encrypt 证书

set -e

DOMAIN="${DOMAIN:-la.us.ucloud.vps.661224.xyz}"
EMAIL="${CERTBOT_EMAIL:-admin@${DOMAIN}}"

echo "=== SSL 证书初始化脚本 ==="
echo "域名: ${DOMAIN}"
echo "邮箱: ${EMAIL}"
echo ""

# 检查 Docker 和 docker-compose 是否可用
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装或不可用"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose 未安装或不可用"
    exit 1
fi

# 检查域名 DNS 解析
echo "检查域名 DNS 解析..."
if ! nslookup "${DOMAIN}" > /dev/null 2>&1; then
    echo "警告: 域名 ${DOMAIN} DNS 解析失败，请确保域名已正确配置"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 启动必要的服务
echo "启动 Caddy 服务以处理 ACME 挑战..."
docker-compose up -d trojan-caddy

# 等待 Caddy 启动
echo "等待 Caddy 服务启动..."
sleep 10

# 检查 Caddy 是否正常运行
if ! docker ps | grep -q "trojan-caddy"; then
    echo "错误: Caddy 服务启动失败"
    exit 1
fi

# 测试 HTTP 访问
echo "测试 HTTP 访问..."
if curl -f "http://${DOMAIN}/.well-known/acme-challenge/test" > /dev/null 2>&1; then
    echo "HTTP 访问正常"
else
    echo "警告: HTTP 访问测试失败，但继续尝试获取证书"
fi

# 启动 certbot 服务
echo "启动 certbot 服务..."
docker-compose up -d certbot

# 等待 certbot 启动
sleep 5

# 手动触发首次证书获取
echo "触发首次证书获取..."
docker-compose exec certbot /usr/local/bin/renew-cert.sh

# 检查证书是否成功获取
if [ -f "./etc/ssl/cert.pem" ] && [ -f "./etc/ssl/key.pem" ]; then
    echo ""
    echo "✅ SSL 证书获取成功！"
    echo "证书文件位置:"
    echo "  - 证书: ./etc/ssl/cert.pem"
    echo "  - 私钥: ./etc/ssl/key.pem"
    echo ""
    echo "现在可以启动完整的服务栈:"
    echo "  docker-compose up -d"
    echo ""
    echo "证书将每天自动检查更新（凌晨 2:30）"
else
    echo ""
    echo "❌ SSL 证书获取失败"
    echo "请检查:"
    echo "  1. 域名 DNS 是否正确指向此服务器"
    echo "  2. 防火墙是否开放 80 端口"
    echo "  3. 服务器是否可以从外网访问"
    echo ""
    echo "查看详细日志:"
    echo "  docker-compose logs certbot"
    exit 1
fi
