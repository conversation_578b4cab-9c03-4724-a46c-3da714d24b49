# Trojan-Go Docker 部署项目

这是一个完整的 Trojan-Go 代理服务器 Docker 部署方案，集成了 Caddy 作为回落服务器和 Certbot 进行 SSL 证书自动化管理。

## 🚀 特性

- **自动化 SSL 证书管理**：集成 Let's Encrypt 和 Certbot
- **智能回落机制**：使用 Caddy 作为伪装网站
- **环境变量配置**：灵活的配置管理
- **Docker Compose 编排**：一键部署和管理
- **健康检查**：完整的监控和状态检查
- **配置优化**：消除冗余配置，统一端口管理

## 📋 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd Trojan

# 复制环境变量配置
cp .env.example .env
```

### 2. 配置环境变量

编辑 `.env` 文件，填入您的配置：

```bash
# 域名配置
DOMAIN=your-domain.com

# Trojan-Go 配置
TROJAN_PASSWORD=your-secure-password
WEBSOCKET_PATH=/your-websocket-path

# 其他配置保持默认即可
```

### 3. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 验证部署

```bash
# 运行状态检查脚本
./scripts/test-services.sh

# 检查健康状态
curl http://localhost:8081/health
```

## 📁 项目结构

```
├── docker-compose.yml          # Docker Compose 配置
├── .env.example               # 环境变量模板
├── etc/
│   ├── caddy/                 # Caddy 配置
│   │   ├── Caddyfile-template # Caddy 配置模板
│   │   ├── start-caddy.sh     # Caddy 启动脚本
│   │   └── html/              # 静态网站文件
│   ├── trojan-go/             # Trojan-Go 配置
│   │   ├── config-template.json # 配置模板
│   │   └── start-trojan.sh    # 启动脚本
│   └── certbot/               # Certbot 配置
│       ├── renew-cert.sh      # 证书续期脚本
│       └── crontab            # 定时任务配置
├── scripts/                   # 工具脚本
│   ├── test-services.sh       # 服务状态检查
│   ├── test-config.sh         # 配置验证
│   └── init-ssl.sh            # SSL 初始化
└── logs/                      # 日志目录（自动生成）
```

## 🔧 服务说明

### Trojan-Go (端口 443)
- 主要的代理服务
- 支持 WebSocket 传输
- 自动回落到 Caddy

### Caddy (端口 7980, 80, 8443)
- 回落服务器和伪装网站
- 处理 ACME 挑战
- 提供 HTTPS 重定向

### Certbot
- 自动获取和续期 SSL 证书
- 定时任务自动运行

### 健康检查 (端口 8081)
- 提供服务状态监控
- 支持外部监控系统集成

## 📖 详细文档

- [Trojan-Go 配置说明](README-TROJAN.md)
- [Caddy 配置说明](README-CADDY.md)
- [SSL 证书管理](README-SSL.md)
- [配置变更日志](CHANGELOG-SIMPLIFY-CONFIG.md)

## 🛠️ 常用命令

```bash
# 重启服务
docker-compose restart

# 查看日志
docker-compose logs trojan-go-wss
docker-compose logs trojan-caddy

# 更新证书
docker-compose exec trojan-certbot /etc/certbot/renew-cert.sh

# 测试配置
./scripts/test-all-config.sh
```

## ⚠️ 注意事项

1. 确保域名 DNS 已正确解析到服务器
2. 防火墙开放必要端口：80, 443, 8081
3. 定期检查证书续期状态
4. 保护好 `.env` 文件中的敏感信息

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
