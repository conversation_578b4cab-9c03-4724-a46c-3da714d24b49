# Caddy 动态配置启动脚本

本项目已集成 Caddy 动态配置生成功能，可以从环境变量自动生成 Caddyfile 并启动服务，为 Trojan-Go 提供完美的伪装 Web 服务。

## 🚀 功能特性

- **动态配置生成**: 从环境变量和配置模板自动生成 Caddyfile
- **伪装 Web 服务**: 为 Trojan-Go 提供真实的 Web 服务回落
- **ACME 挑战支持**: 自动处理 Let's Encrypt 证书验证
- **多端口支持**: HTTP、HTTPS、健康检查等多端口配置
- **安全响应头**: 自动添加安全相关的 HTTP 响应头
- **日志记录**: 完整的访问日志和错误日志

## 📁 文件结构

```
├── etc/caddy/
│   ├── Caddyfile-template     # Caddy 配置模板
│   ├── Caddyfile             # 生成的配置文件（运行时创建）
│   ├── start-caddy.sh        # Caddy 启动脚本
│   └── html/                 # Web 根目录
├── logs/caddy/               # Caddy 日志目录
└── scripts/
    └── test-caddy-config.sh  # Caddy 配置测试脚本
```

## ⚙️ 环境变量配置

### 端口配置变量

```env
# 统一的回落端口配置
FALLBACK_PORT=7980           # Trojan-Go 回落端口，也是 Caddy 服务端口
HTTPS_PORT=8443              # HTTPS 服务端口（可选）
HEALTH_CHECK_PORT=8080       # 健康检查端口

# 基础配置
DOMAIN=your-domain.com       # 服务域名
```

### 简化的端口配置

**优化**: 使用统一的 `FALLBACK_PORT` 变量，避免配置冗余：

```env
# 统一配置 - 无需重复设置
FALLBACK_PORT=7980           # Trojan-Go 和 Caddy 共享的回落端口
```

这样避免了之前 `REMOTE_PORT` 和 `CADDY_PORT` 必须保持一致的配置冗余问题。

## 🔧 配置模板功能

### 支持的变量替换

- `${DOMAIN}` - 服务域名
- `${FALLBACK_PORT}` - Trojan-Go 回落端口，也是 Caddy HTTP 服务端口
- `${HTTPS_PORT}` - HTTPS 服务端口
- `${HEALTH_CHECK_PORT}` - 健康检查端口

### 自动配置功能

1. **ACME 挑战处理**: 自动在 80 端口处理 Let's Encrypt 验证
2. **HTTPS 重定向**: HTTP 请求自动重定向到 HTTPS
3. **SSL 证书检查**: 自动检测 SSL 证书文件存在性
4. **默认页面生成**: 自动创建 index.html 和 404.html
5. **安全响应头**: 自动添加安全相关的 HTTP 头

## 🚀 使用方法

### 1. 配置环境变量

确保 `.env` 文件中的端口配置正确：

```bash
# 检查回落端口配置
grep "FALLBACK_PORT" .env
```

### 2. 测试配置

运行 Caddy 配置测试：

```bash
./scripts/test-caddy-config.sh
```

### 3. 启动服务

#### 单独启动 Caddy 服务

```bash
docker-compose up trojan-caddy
```

#### 启动所有服务

```bash
docker-compose up -d
```

## 🔍 Caddy 启动脚本工作流程

1. **环境变量验证**: 检查必需的环境变量
2. **文件检查**: 验证配置模板和 SSL 证书文件
3. **目录创建**: 创建必要的日志和 Web 目录
4. **配置生成**: 基于模板和环境变量生成 Caddyfile
5. **配置验证**: 使用 `caddy validate` 验证配置
6. **默认页面**: 创建默认的 HTML 页面
7. **服务启动**: 启动 Caddy 服务

## 📝 服务端点说明

### HTTP 服务 (端口 80)
- **ACME 挑战**: `/.well-known/acme-challenge/*`
- **HTTPS 重定向**: 其他所有请求重定向到 HTTPS

### 伪装 Web 服务 (FALLBACK_PORT)
- **静态文件服务**: 提供 `/var/www/html` 目录下的文件
- **Trojan-Go 回落**: 当 Trojan-Go 检测到非法连接时回落到此服务
- **安全响应头**: 自动添加安全相关的 HTTP 头
- **404 处理**: 自定义 404 错误页面

### HTTPS 服务 (HTTPS_PORT)
- **SSL 终端**: 使用 SSL 证书提供 HTTPS 服务
- **静态资源缓存**: 自动设置静态资源缓存头
- **安全策略**: HSTS、CSP 等安全响应头

### 健康检查 (HEALTH_CHECK_PORT)
- `/health` - 返回 "OK"
- `/status` - 返回服务状态
- `/` - 返回简单状态信息

## 🛠️ 故障排除

### 1. 端口冲突

**检查端口配置**:
```bash
# 查看当前端口配置
grep -E "FALLBACK_PORT|HTTPS_PORT|HEALTH_CHECK_PORT" .env

# 检查端口是否被占用
netstat -tlnp | grep -E "(80|443|7980|8080|8443)"
```

### 2. 配置文件错误

**验证配置**:
```bash
# 运行配置测试
./scripts/test-caddy-config.sh

# 手动验证 Caddyfile
docker-compose exec trojan-caddy caddy validate --config /etc/caddy/Caddyfile
```

### 3. SSL 证书问题

**检查证书**:
```bash
# 查看证书文件
ls -la etc/ssl/

# 验证证书有效性
openssl x509 -in etc/ssl/cert.pem -text -noout
```

### 4. Web 服务无法访问

**检查服务状态**:
```bash
# 查看 Caddy 日志
docker-compose logs trojan-caddy

# 测试健康检查端点
curl http://localhost:8080/health
```

## 📊 日志管理

### 日志文件位置

- **访问日志**: `logs/caddy/access.log`
- **HTTPS 访问日志**: `logs/caddy/https_access.log`
- **错误日志**: Docker 容器日志

### 日志轮转配置

- **文件大小**: 100MB 自动轮转
- **保留数量**: 5 个历史文件
- **保留时间**: 720 小时（30天）

### 查看日志

```bash
# 实时查看 Caddy 日志
docker-compose logs -f trojan-caddy

# 查看访问日志
tail -f logs/caddy/access.log

# 分析访问统计
jq '.request.uri' logs/caddy/access.log | sort | uniq -c | sort -nr
```

## 🔒 安全配置

### 自动添加的安全响应头

- `Server`: 伪装为 nginx
- `X-Frame-Options`: 防止点击劫持
- `X-Content-Type-Options`: 防止 MIME 类型嗅探
- `X-XSS-Protection`: XSS 保护
- `Strict-Transport-Security`: HTTPS 强制（HTTPS 服务）
- `Referrer-Policy`: 引用策略

### 访问控制

可以在配置模板中添加 IP 白名单或访问限制：

```caddyfile
# 示例：限制管理端点访问
handle /admin/* {
    @allowed {
        remote_ip ***********/24 10.0.0.0/8
    }
    abort @allowed
}
```

## 🔄 配置更新

当需要更新 Caddy 配置时：

1. 修改 `.env` 文件中的环境变量
2. 重启 Caddy 服务：
   ```bash
   docker-compose restart trojan-caddy
   ```
3. 配置将自动重新生成并应用

## 🎯 与 Trojan-Go 的集成

### 代理流程

1. **正常流量**: Trojan-Go 处理合法的代理请求
2. **伪装流量**: 非法连接被转发到 Caddy (CADDY_PORT)
3. **Web 响应**: Caddy 返回真实的 Web 页面，完美伪装

### 配置要点

- Trojan-Go 和 Caddy 共享同一个 `FALLBACK_PORT`
- Caddy 提供真实的 Web 服务，增强伪装效果
- 支持 WebSocket 升级，与 Trojan-Go 的 WebSocket 模式兼容
