# Trojan-Go 动态配置启动脚本

本项目已集成动态配置生成功能，可以从环境变量自动生成 Trojan-Go 配置文件并启动服务。

## 🚀 功能特性

- **动态配置生成**: 从环境变量和配置模板自动生成配置文件
- **灵活配置**: 支持所有主要的 Trojan-Go 配置选项
- **安全启动**: 自动验证配置文件格式和必要文件
- **日志记录**: 详细的启动日志和配置信息
- **容器化**: 完全支持 Docker 容器环境

## 📁 文件结构

```
├── etc/trojan-go/
│   ├── config-template.json  # 配置模板文件
│   ├── config.json          # 生成的配置文件（运行时创建）
│   └── start-trojan.sh      # 启动脚本
├── scripts/
│   └── simple-test.sh       # 配置测试脚本
└── .env                     # 环境变量配置
```

## ⚙️ 环境变量配置

在 `.env` 文件中配置以下变量：

### 必需变量

```env
# 基础配置
DOMAIN=your-domain.com                    # 服务域名
TROJAN_PASSWORD=your-secure-password      # Trojan 密码
WEBSOCKET_PATH=/your-websocket-path       # WebSocket 路径

# 网络配置
LOCAL_PORT=443                            # 本地监听端口
REMOTE_PORT=7980                          # 远程转发端口

# SSL 证书配置
SSL_CERT=/etc/ssl/cloudflare/cert.pem     # SSL 证书路径
SSL_KEY=/etc/ssl/cloudflare/key.pem       # SSL 私钥路径
```

### 可选变量

```env
# 日志配置
LOG_LEVEL=1                               # 日志级别 (0-5)
```

## 🔧 配置模板

配置模板 `etc/trojan-go/config-template.json` 支持以下变量替换：

- `${DOMAIN}` - 服务域名
- `${TROJAN_PASSWORD}` - Trojan 密码
- `${WEBSOCKET_PATH}` - WebSocket 路径
- `${LOCAL_PORT}` - 本地端口
- `${REMOTE_PORT}` - 远程端口
- `${LOG_LEVEL}` - 日志级别
- `${SSL_CERT}` - SSL 证书路径
- `${SSL_KEY}` - SSL 私钥路径

## 🚀 使用方法

### 1. 配置环境变量

编辑 `.env` 文件，设置您的配置：

```bash
vim .env
```

### 2. 测试配置

运行测试脚本验证配置：

```bash
./scripts/simple-test.sh
```

### 3. 启动服务

#### 单独启动 Trojan-Go 服务

```bash
docker-compose up trojan-go-wss
```

#### 启动所有服务

```bash
docker-compose up -d
```

## 🔍 启动脚本工作流程

1. **环境变量验证**: 检查必需的环境变量是否设置
2. **文件检查**: 验证配置模板和 SSL 证书文件
3. **配置生成**: 基于模板和环境变量生成配置文件
4. **格式验证**: 验证生成的 JSON 配置文件格式
5. **服务启动**: 使用生成的配置启动 Trojan-Go

## 📝 常用命令

### 查看服务状态

```bash
# 查看所有服务状态
docker-compose ps

# 查看 Trojan-Go 日志
docker-compose logs trojan-go-wss

# 实时查看日志
docker-compose logs -f trojan-go-wss
```

### 重启服务

```bash
# 重启 Trojan-Go 服务
docker-compose restart trojan-go-wss

# 重新构建并启动
docker-compose up -d --force-recreate trojan-go-wss
```

### 配置调试

```bash
# 查看生成的配置文件
docker-compose exec trojan-go-wss cat /etc/trojan-go/config.json

# 验证配置文件格式
docker-compose exec trojan-go-wss jq empty /etc/trojan-go/config.json
```

## 🛠️ 故障排除

### 1. 服务启动失败

**检查日志**:
```bash
docker-compose logs trojan-go-wss
```

**常见问题**:
- 环境变量未设置或格式错误
- SSL 证书文件不存在或权限问题
- 端口被占用

### 2. 配置文件错误

**验证环境变量**:
```bash
./scripts/simple-test.sh
```

**手动检查配置**:
```bash
# 查看环境变量
cat .env

# 查看配置模板
cat etc/trojan-go/config-template.json
```

### 3. SSL 证书问题

**检查证书文件**:
```bash
# 检查证书是否存在
ls -la etc/ssl/

# 验证证书有效性
openssl x509 -in etc/ssl/cert.pem -text -noout
```

## 🔒 安全注意事项

1. **密码安全**: 使用强密码并定期更换
2. **证书管理**: 确保 SSL 证书及时更新
3. **端口安全**: 仅开放必要的端口
4. **日志管理**: 定期清理和轮转日志文件
5. **权限控制**: 确保配置文件权限设置正确

## 📊 性能优化

### 连接优化

配置模板已包含以下优化设置：

- **多路复用**: 启用 mux 提高连接效率
- **会话复用**: 启用 SSL 会话复用
- **TCP 优化**: 启用 TCP_NODELAY 和 keep-alive

### 资源限制

在 `docker-compose.yml` 中可以添加资源限制：

```yaml
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '0.5'
```

## 🔄 配置更新

当需要更新配置时：

1. 修改 `.env` 文件中的环境变量
2. 重启服务以应用新配置：
   ```bash
   docker-compose restart trojan-go-wss
   ```

配置将自动重新生成并应用。
